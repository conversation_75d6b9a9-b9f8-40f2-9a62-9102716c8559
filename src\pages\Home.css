.home-page {
  padding: 1rem;
  animation: fadeIn 0.5s ease-in-out;
  max-width: 100%;
  overflow-x: hidden;
}

.home-header {
  margin-bottom: 2rem;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  width: 100%;
}

.logo-welcome-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.home-logo {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  object-fit: cover;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.welcome-text {
  flex: 1;
  min-width: 0;
}

.home-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.home-subtitle {
  font-size: 0.8rem;
  color: #6c757d;
  margin: 0;
  font-weight: 400;
  line-height: 1.3;
}

.notification-section {
  flex-shrink: 0;
  margin-left: 0.5rem;
}

.notification-button {
  position: relative;
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0.4rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-button:hover {
  background: rgba(0, 123, 255, 0.1);
  border-radius: 8px;
  transform: scale(1.05);
}

.notification-icon {
  font-size: 1.8rem;
  color: #007bff;
  transition: color 0.3s ease;
}

.notification-button:hover .notification-icon {
  color: #0056b3;
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #dc3545;
  color: white;
  font-size: 0.7rem;
  font-weight: 700;
  padding: 0.2rem 0.4rem;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
  line-height: 1;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.user-info {
  text-align: left;
  padding: 0.5rem 0;
}

.user-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.user-role {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
  font-weight: 400;
}

.main-sections {
  padding: 0;
}

.sections-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.training-section {
  margin-top: 1rem;
}

/* Very small mobile screens */
@media (max-width: 360px) {
  .home-page {
    padding: 0.5rem;
  }

  .sections-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.4rem;
  }

  .main-sections {
    padding: 0;
  }
}

/* Mobile-first responsive design */
@media (min-width: 361px) and (max-width: 479px) {
  .home-page {
    padding: 0.75rem;
  }

  .home-logo {
    width: 45px;
    height: 45px;
  }

  .home-title {
    font-size: 1rem;
  }

  .home-subtitle {
    font-size: 0.75rem;
  }

  .user-name {
    font-size: 1.1rem;
  }

  .user-role {
    font-size: 0.8rem;
  }

  .sections-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
  }

  .notification-icon {
    font-size: 1.6rem;
  }

  .notification-button {
    padding: 0.3rem;
  }

  .notification-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.35rem;
    min-width: 18px;
  }
}

/* Small tablets and large phones */
@media (min-width: 480px) and (max-width: 767px) {
  .home-page {
    padding: 1rem;
  }

  .home-logo {
    width: 55px;
    height: 55px;
  }

  .home-title {
    font-size: 1.2rem;
  }

  .home-subtitle {
    font-size: 0.85rem;
  }

  .sections-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  .user-name {
    font-size: 1.3rem;
  }

  .notification-icon {
    font-size: 1.9rem;
  }

  .notification-button {
    padding: 0.35rem;
  }
}

/* Tablets */
@media (min-width: 768px) and (max-width: 1023px) {
  .home-page {
    padding: 1.5rem;
  }

  .home-logo {
    width: 60px;
    height: 60px;
  }

  .home-title {
    font-size: 1.4rem;
  }

  .home-subtitle {
    font-size: 0.9rem;
  }

  .user-name {
    font-size: 1.4rem;
  }

  .user-role {
    font-size: 0.95rem;
  }

  .sections-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.25rem;
  }

  .notification-icon {
    font-size: 2rem;
  }

  .notification-button {
    padding: 0.4rem;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .home-page {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  .home-logo {
    width: 70px;
    height: 70px;
  }

  .home-title {
    font-size: 1.6rem;
  }

  .home-subtitle {
    font-size: 1rem;
  }

  .user-name {
    font-size: 1.5rem;
  }

  .user-role {
    font-size: 1rem;
  }

  .sections-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    max-width: 900px;
    margin: 0 auto 1.5rem auto;
  }

  .training-section {
    max-width: 900px;
    margin: 1.5rem auto 0;
  }

  .notification-icon {
    font-size: 2.2rem;
  }

  .notification-button {
    padding: 0.5rem;
  }
}

/* Large desktop */
@media (min-width: 1440px) {
  .home-page {
    padding: 2.5rem;
  }

  .sections-grid {
    max-width: 1000px;
    gap: 2rem;
  }

  .training-section {
    max-width: 1000px;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sections-grid > * {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.sections-grid > *:nth-child(1) { animation-delay: 0.1s; }
.sections-grid > *:nth-child(2) { animation-delay: 0.2s; }
.sections-grid > *:nth-child(3) { animation-delay: 0.3s; }
.sections-grid > *:nth-child(4) { animation-delay: 0.4s; }
.sections-grid > *:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
