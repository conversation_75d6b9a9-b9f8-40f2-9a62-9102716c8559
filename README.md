# Medcare PWA - Progressive Web Application

A modern, mobile-first Progressive Web Application built with React + Vite for medical care management.

## 🚀 Features

### PWA Capabilities
- **Offline Support**: Service worker caching for offline functionality
- **Install Prompt**: Native app-like installation experience
- **Mobile-First Design**: Optimized for mobile devices with responsive breakpoints
- **Touch-Friendly**: Optimized touch interactions and gestures

### Navigation & UI
- **Fixed Bottom Navigation**: 5-tab navigation (Dashboard, Search, Home, Offline Modules, Profile)
- **Smooth Page Transitions**: Animated transitions between pages
- **Modern UI Design**: Rounded corners, drop shadows, and hover/tap animations
- **Responsive Layout**: Adapts to different screen sizes

### Home Page Sections
- **Modules**: Access learning modules and educational content
- **Classroom**: Join virtual classrooms and interactive sessions
- **Checklist**: Track progress and complete tasks
- **Documents**: View and manage important documents
- **Forms**: Fill out and submit required forms
- **Certificates**: View and manage professional certificates
- **Analytics**: Access performance analytics and insights
- **Assessments**: Take medical assessments and evaluations

## 🛠️ Technology Stack

- **Frontend**: React 19.1.1
- **Build Tool**: Vite 5.4.0
- **Routing**: React Router DOM 7.8.0
- **Icons**: React Icons 5.5.0
- **PWA**: Vite Plugin PWA 1.0.2
- **Service Worker**: Workbox 7.3.0
- **Charts**: Highcharts with React integration

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── FooterNavigation.jsx
│   ├── Layout.jsx
│   ├── PageTransition.jsx
│   └── SectionCard.jsx
├── pages/              # Page components
│   ├── Dashboard.jsx
│   ├── Home.jsx
│   ├── OfflineModules.jsx
│   ├── Profile.jsx
│   └── Search.jsx
├── styles/             # CSS files
├── App.jsx             # Main app component
├── main.jsx           # App entry point
└── index.css          # Global styles
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v20.11.0 or higher recommended)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd medcare
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5174`

### Building for Production

1. Build the application:
```bash
npm run build
```

2. Preview the production build:
```bash
npm run preview
```

## 📱 PWA Features

### Service Worker
- Automatic caching of static assets
- Offline functionality for previously visited pages
- Background sync capabilities

### Manifest
- App name: "Medcare PWA"
- Theme color: #007bff
- Display mode: standalone
- Start URL: /

### Installation
The app can be installed on mobile devices and desktops through the browser's "Add to Home Screen" or "Install App" feature.

## 🎨 Design Features

### Mobile-First Approach
- Responsive grid layouts
- Touch-friendly button sizes (minimum 44px)
- Optimized typography for mobile reading
- Smooth animations and transitions

### Color Scheme
- Primary: #007bff (Blue)
- Secondary: #6c757d (Gray)
- Success: #28a745 (Green)
- Background: #f8f9fa (Light Gray)

### Animations
- Page transitions with fade and slide effects
- Card hover animations
- Button press feedback
- Loading states

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Adding New Pages

1. Create a new component in `src/pages/`
2. Add the route in `src/App.jsx`
3. Update navigation in `src/components/FooterNavigation.jsx` if needed

### Customizing Styles

The app uses CSS custom properties (variables) defined in `src/index.css` for consistent theming:

```css
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  /* ... more variables */
}
```

## 📊 Performance

- Lighthouse PWA Score: 100/100 (when properly configured)
- Mobile-optimized bundle size
- Lazy loading for optimal performance
- Service worker caching for fast subsequent loads

## 🔒 Security

- Content Security Policy headers
- HTTPS required for PWA features
- Secure service worker implementation

## 🌐 Browser Support

- Chrome/Chromium 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions, please open an issue in the repository.
