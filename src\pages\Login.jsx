import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { MdEmail, MdLock, MdVisibility, MdVisibilityOff, MdSecurity } from 'react-icons/md'
import logoImage from '../assets/logo.jpg'
import './Login.css'

const Login = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [acceptedPrivacyPolicy, setAcceptedPrivacyPolicy] = useState(false)
  const [showPrivacyModal, setShowPrivacyModal] = useState(false)
  const [errors, setErrors] = useState({})
  const [isLoading, setIsLoading] = useState(false)

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    if (!acceptedPrivacyPolicy) {
      newErrors.privacy = 'You must accept the Privacy Policy to continue'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // For demo purposes, accept any valid email/password
      // In real app, this would be an actual authentication API call
      console.log('Login attempt:', formData)
      
      // Navigate to home page on successful login
      navigate('/home')
    } catch (error) {
      setErrors({ submit: 'Login failed. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const isFormValid = formData.email && formData.password && acceptedPrivacyPolicy && !isLoading

  return (
    <div className="login-page">
      <div className="login-container">
        <div className="login-header">
          <img src={logoImage} alt="Medcare Logo" className="login-logo" />
          <h1 className="login-title">Welcome to Medcare</h1>
          <p className="login-subtitle">Sign in to your medical care platform</p>
        </div>

        <form className="login-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              <MdEmail className="label-icon" />
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`form-input ${errors.email ? 'error' : ''}`}
              placeholder="Enter your email address"
              autoComplete="email"
              disabled={isLoading}
            />
            {errors.email && <span className="error-message">{errors.email}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              <MdLock className="label-icon" />
              Password
            </label>
            <div className="password-input-container">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className={`form-input ${errors.password ? 'error' : ''}`}
                placeholder="Enter your password"
                autoComplete="current-password"
                disabled={isLoading}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={togglePasswordVisibility}
                disabled={isLoading}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <MdVisibilityOff /> : <MdVisibility />}
              </button>
            </div>
            {errors.password && <span className="error-message">{errors.password}</span>}
          </div>

          <div className="form-group privacy-group">
            <label className="privacy-checkbox-label">
              <input
                type="checkbox"
                checked={acceptedPrivacyPolicy}
                onChange={(e) => setAcceptedPrivacyPolicy(e.target.checked)}
                className="privacy-checkbox"
                disabled={isLoading}
              />
              <span className="checkbox-custom"></span>
              <span className="privacy-text">
                I accept the{' '}
                <button
                  type="button"
                  className="privacy-link"
                  onClick={() => setShowPrivacyModal(true)}
                  disabled={isLoading}
                >
                  Privacy Policy
                </button>
              </span>
            </label>
            {errors.privacy && <span className="error-message">{errors.privacy}</span>}
          </div>

          {errors.submit && (
            <div className="submit-error">
              {errors.submit}
            </div>
          )}

          <button
            type="submit"
            className={`login-button ${!isFormValid ? 'disabled' : ''}`}
            disabled={!isFormValid}
          >
            {isLoading ? (
              <span className="loading-spinner"></span>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        <div className="login-footer">
          <p className="security-note">
            <MdSecurity className="security-icon" />
            Your data is protected with enterprise-grade security
          </p>
        </div>
      </div>

      {/* Privacy Policy Modal */}
      {showPrivacyModal && (
        <div className="privacy-modal-overlay" onClick={() => setShowPrivacyModal(false)}>
          <div className="privacy-modal" onClick={(e) => e.stopPropagation()}>
            <div className="privacy-modal-header">
              <h2>Privacy Policy</h2>
              <button
                className="privacy-modal-close"
                onClick={() => setShowPrivacyModal(false)}
                aria-label="Close privacy policy"
              >
                ×
              </button>
            </div>
            <div className="privacy-modal-content">
              <h3>Data Collection and Use</h3>
              <p>
                Medcare collects and processes your personal information to provide medical care services,
                maintain your health records, and improve our platform. We are committed to protecting
                your privacy and ensuring the security of your medical data.
              </p>
              
              <h3>Information We Collect</h3>
              <ul>
                <li>Personal identification information (name, email, phone number)</li>
                <li>Medical history and health records</li>
                <li>Usage data and platform interactions</li>
                <li>Device information and technical data</li>
              </ul>

              <h3>Data Security</h3>
              <p>
                We implement industry-standard security measures including encryption, secure servers,
                and regular security audits to protect your sensitive medical information.
              </p>

              <h3>Your Rights</h3>
              <p>
                You have the right to access, update, or delete your personal information. You may also
                request data portability or restrict processing of your data.
              </p>

              <h3>Contact Us</h3>
              <p>
                For questions about this Privacy Policy, please contact our Privacy Officer at
                <EMAIL> or call 1-800-MEDCARE.
              </p>
            </div>
            <div className="privacy-modal-footer">
              <button
                className="privacy-accept-button"
                onClick={() => {
                  setAcceptedPrivacyPolicy(true)
                  setShowPrivacyModal(false)
                }}
              >
                Accept Privacy Policy
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Login
