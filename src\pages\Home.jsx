import {
  MdSchool,
  MdClass,
  MdChecklist,
  MdDescription,
  MdAssignment,
  MdNotifications,
  MdCardMembership,
  MdAnalytics,
  MdQuiz
} from 'react-icons/md'
import SectionIcon from '../components/SectionIcon'
import OnlineTraining from '../components/OnlineTraining'
import logoImage from '../assets/logo.jpg'
import './Home.css'

const Home = () => {
  const sections = [
    {
      id: 'modules',
      title: 'Modules',
      icon: MdSchool,
      color: '#4CAF50'
    },
    {
      id: 'classroom',
      title: 'Classroom',
      icon: MdClass,
      color: '#2196F3'
    },
    {
      id: 'checklist',
      title: 'Checklist',
      icon: MdChecklist,
      color: '#FF9800'
    },
    {
      id: 'documents',
      title: 'Documents',
      icon: MdDescription,
      color: '#9C27B0'
    },
    {
      id: 'forms',
      title: 'Forms',
      icon: MdAssignment,
      color: '#F44336'
    },
    {
      id: 'certificates',
      title: 'Certificates',
      icon: MdCardMembership,
      color: '#795548'
    },
    {
      id: 'analytics',
      title: 'Analytics',
      icon: MdAnalytics,
      color: '#607D8B'
    },
    {
      id: 'assessments',
      title: 'Assessments',
      icon: MdQuiz,
      color: '#E91E63'
    }
  ]

  return (
    <div className="home-page">
      <header className="home-header">
        <div className="header-top">
          <div className="logo-welcome-section">
            <img src={logoImage} alt="Medcare Logo" className="home-logo" />
            <div className="welcome-text">
              <h1 className="home-title">Welcome to Medcare</h1>
              <p className="home-subtitle">Your comprehensive medical care platform</p>
            </div>
          </div>
          <div className="notification-section">
            <button className="notification-button">
              <MdNotifications className="notification-icon" />
              <span className="notification-badge">3</span>
            </button>
          </div>
        </div>

        <div className="user-info">
          <h2 className="user-name">Dr. John Smith</h2>
          <p className="user-role">Medical Professional</p>
        </div>
      </header>

      <div className="main-sections">
        <div className="sections-grid">
          {sections.map((section) => (
            <SectionIcon
              key={section.id}
              title={section.title}
              icon={section.icon}
              color={section.color}
              onClick={() => {
                console.log(`Clicked on ${section.title}`)
              }}
            />
          ))}
        </div>

        <div className="training-section">
          <OnlineTraining />
        </div>
      </div>
    </div>
  )
}

export default Home
