import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import './OnlineTraining.css'

const OnlineTraining = () => {
  // Knowledge Index Score (85% in this example)
  const knowledgeScore = 85;
  // Pull Rate (static example)
  const pullRate = 92;

  // Function to get color based on Knowledge Index score
  // Risk Levels:
  // 80-100%: Green (#4CAF50) - Low Risk
  // 60-79%:  Blue (#2196F3) - Lucky
  // 40-59%:  Orange (#FF9800) - Risky
  // 0-39%:   Red (#F44336) - High Risk
  const getKnowledgeIndexColor = (score) => {
    if (score >= 80) return '#4CAF50'; // Green - Low Risk
    if (score >= 60) return '#2196F3'; // Blue - Lucky
    if (score >= 40) return '#FF9800'; // Orange - Risky
    return '#F44336'; // Red - High Risk
  };

  // Get the dynamic color for the current score
  const knowledgeColor = getKnowledgeIndexColor(knowledgeScore);

  // Helper to draw perfectly centered text in donut charts
  const renderCenterLabel = (chart, key, text) => {
    const series = chart.series && chart.series[0]
    if (!series || !series.center) return
    const [cx, cy] = series.center // [x, y, diameter]
    const x = chart.plotLeft + cx
    const y = chart.plotTop + cy
    const prop = `${key}Label`
    const style = { fontSize: '18px', fontWeight: '700', color: '#2c3e50', textAlign: 'center' }

    if (!chart[prop]) {
      chart[prop] = chart.renderer
        .text(text)
        .attr({ align: 'center', zIndex: 7 })
        .css(style)
        .add()
    }
    // Always update position and text
    chart[prop].attr({ text, x, y })
    // Use dy to correct baseline to optical center (~0.35em)
    chart[prop].attr({ dy: 6 })
  }

  // Knowledge Index Score Chart Configuration
  const knowledgeIndexOptions = {
    chart: {
      type: 'pie',
      height: 150,
      width: 150,
      backgroundColor: 'transparent',
      margin: [20, 20, 20, 20],
      spacing: [10, 10, 10, 10],
      events: {
        render: function () {
          renderCenterLabel(this, 'knowledgeCenter', `${knowledgeScore}%`)
        }
      }
    },
    title: {
      text: 'Knowledge Index Score',
      style: {
        fontSize: '10px',
        fontWeight: '600',
        color: '#2c3e50'
      },
      margin: 3,
      y: 12
    },
    plotOptions: {
      pie: {
        innerSize: '70%',
        dataLabels: {
          enabled: false
        },
        showInLegend: false
      }
    },
    colors: [knowledgeColor, '#e9ecef'],
    series: [{
      name: 'Score',
      data: [
        {
          name: 'Completed',
          y: knowledgeScore,
          color: knowledgeColor
        },
        {
          name: 'Remaining',
          y: 100 - knowledgeScore,
          color: '#e9ecef'
        }
      ]
    }],
    credits: {
      enabled: false
    },
    tooltip: {
      pointFormat: '<b>{point.percentage:.1f}%</b>'
    }
  }

  // Pull Rate Chart Configuration
  const pullRateOptions = {
    chart: {
      type: 'pie',
      height: 150,
      width: 150,
      backgroundColor: 'transparent',
      margin: [20, 20, 20, 20],
      spacing: [10, 10, 10, 10],
      events: {
        render: function () {
          renderCenterLabel(this, 'pullCenter', `${pullRate}%`)
        }
      }
    },
    title: {
      text: 'Pull Rate',
      style: {
        fontSize: '10px',
        fontWeight: '600',
        color: '#2c3e50'
      },
      margin: 3,
      y: 12
    },
    plotOptions: {
      pie: {
        innerSize: '70%',
        dataLabels: {
          enabled: false
        },
        showInLegend: false
      }
    },
    series: [{
      name: 'Rate',
      data: [
        { name: 'Success', y: 92, color: '#2196F3' },
        { name: 'Remaining', y: 8, color: '#e9ecef' }
      ]
    }],
    credits: {
      enabled: false
    },
    tooltip: {
      pointFormat: '<b>{point.percentage:.1f}%</b>'
    }
  }

  return (
    <div className="online-training">
      <h3 className="training-title">Online Training</h3>
      <div className="training-charts">
        <div className="chart-container">
          <HighchartsReact
            highcharts={Highcharts}
            options={knowledgeIndexOptions}
          />
        </div>

        <div className="chart-container">
          <HighchartsReact
            highcharts={Highcharts}
            options={pullRateOptions}
          />
        </div>
      </div>
    </div>
  )
}

export default OnlineTraining
