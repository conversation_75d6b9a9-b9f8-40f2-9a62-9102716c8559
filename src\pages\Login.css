/* Login Page Styles */
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1rem;
}

.login-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 400px;
  position: relative;
}

/* Header Section */
.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.login-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.login-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.label-icon {
  font-size: 1.1rem;
  color: var(--primary-color);
}

.form-input {
  padding: 0.875rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: var(--text-primary);
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  outline: none;
}

.form-input.error {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Password Input Container */
.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-container .form-input {
  padding-right: 3rem;
  flex: 1;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.password-toggle:hover {
  color: var(--primary-color);
  background: rgba(0, 123, 255, 0.1);
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Privacy Policy Section */
.privacy-group {
  margin-top: 0.5rem;
}

.privacy-checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.9rem;
  line-height: 1.4;
}

.privacy-checkbox {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.privacy-checkbox:checked + .checkbox-custom {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.privacy-checkbox:checked + .checkbox-custom::after {
  content: '✓';
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.privacy-text {
  color: var(--text-secondary);
}

.privacy-link {
  color: var(--primary-color);
  background: none;
  border: none;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  padding: 0;
  transition: color 0.3s ease;
}

.privacy-link:hover {
  color: #0056b3;
}

.privacy-link:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Error Messages */
.error-message {
  color: var(--danger-color);
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.submit-error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.9rem;
  text-align: center;
  border: 1px solid #f5c6cb;
}

/* Login Button */
.login-button {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 52px;
}

.login-button:hover:not(.disabled) {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.login-button.disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer Section */
.login-footer {
  margin-top: 2rem;
  text-align: center;
}

.security-note {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0;
}

.security-icon {
  color: var(--success-color);
  font-size: 1rem;
}

/* Privacy Policy Modal */
.privacy-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.privacy-modal {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.privacy-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.privacy-modal-header h2 {
  margin: 0;
  color: var(--text-primary);
}

.privacy-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.privacy-modal-close:hover {
  background: #f8f9fa;
  color: var(--text-primary);
}

.privacy-modal-content {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.privacy-modal-content h3 {
  color: var(--text-primary);
  margin: 1.5rem 0 0.75rem 0;
  font-size: 1.1rem;
}

.privacy-modal-content h3:first-child {
  margin-top: 0;
}

.privacy-modal-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.privacy-modal-content ul {
  color: var(--text-secondary);
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.privacy-modal-content li {
  margin-bottom: 0.5rem;
}

.privacy-modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
}

.privacy-accept-button {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.privacy-accept-button:hover {
  background: #0056b3;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-page {
    padding: 0.5rem;
  }

  .login-container {
    padding: 1.5rem;
    border-radius: 12px;
  }

  .login-logo {
    width: 60px;
    height: 60px;
  }

  .login-title {
    font-size: 1.5rem;
  }

  .login-subtitle {
    font-size: 0.9rem;
  }

  .form-input {
    padding: 0.75rem;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .privacy-modal {
    margin: 0.5rem;
    max-height: 90vh;
  }

  .privacy-modal-header,
  .privacy-modal-content,
  .privacy-modal-footer {
    padding: 1rem;
  }
}

@media (max-width: 360px) {
  .login-container {
    padding: 1rem;
  }

  .login-title {
    font-size: 1.25rem;
  }

  .form-group {
    gap: 0.375rem;
  }

  .login-form {
    gap: 1.25rem;
  }
}
