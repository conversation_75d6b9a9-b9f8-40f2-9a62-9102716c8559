import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { MdPerson, MdSettings, MdNotifications, MdSecurity, MdHelp, MdLogout } from 'react-icons/md'
import './PageStyles.css'

const Profile = () => {
  const navigate = useNavigate()
  const [showLogoutModal, setShowLogoutModal] = useState(false)

  const handleLogout = () => {
    // Clear any stored authentication data
    localStorage.removeItem('authToken')
    localStorage.removeItem('userSession')
    sessionStorage.clear()

    // Navigate to login page
    navigate('/')
  }

  const confirmLogout = () => {
    setShowLogoutModal(true)
  }

  const cancelLogout = () => {
    setShowLogoutModal(false)
  }

  const profileOptions = [
    {
      icon: MdSettings,
      title: 'Account Settings',
      description: 'Manage your account preferences'
    },
    {
      icon: MdNotifications,
      title: 'Notifications',
      description: 'Configure notification preferences'
    },
    {
      icon: MdSecurity,
      title: 'Privacy & Security',
      description: 'Manage your privacy settings'
    },
    {
      icon: MdHelp,
      title: 'Help & Support',
      description: 'Get help and contact support'
    }
  ]

  return (
    <div className="page-container">
      <header className="page-header">
        <h1 className="page-title">Profile</h1>
        <p className="page-subtitle">Manage your account and preferences</p>
      </header>
      
      <div className="profile-info">
        <div className="profile-avatar">
          <MdPerson className="avatar-icon" />
        </div>
        <div className="profile-details">
          <h2 className="profile-name">Dr. John Smith</h2>
          <p className="profile-email"><EMAIL></p>
          <p className="profile-role">Medical Professional</p>
        </div>
      </div>
      
      <div className="profile-options">
        {profileOptions.map((option, index) => {
          const IconComponent = option.icon
          return (
            <div key={index} className="profile-option">
              <div className="option-icon">
                <IconComponent />
              </div>
              <div className="option-content">
                <h3 className="option-title">{option.title}</h3>
                <p className="option-description">{option.description}</p>
              </div>
              <div className="option-arrow">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 18l6-6-6-6" />
                </svg>
              </div>
            </div>
          )
        })}
        
        <div className="profile-option logout-option" onClick={confirmLogout}>
          <div className="option-icon">
            <MdLogout />
          </div>
          <div className="option-content">
            <h3 className="option-title">Sign Out</h3>
            <p className="option-description">Sign out of your account</p>
          </div>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutModal && (
        <div className="logout-modal-overlay" onClick={cancelLogout}>
          <div className="logout-modal" onClick={(e) => e.stopPropagation()}>
            <div className="logout-modal-header">
              <h2>Confirm Sign Out</h2>
            </div>
            <div className="logout-modal-content">
              <p>Are you sure you want to sign out of your Medcare account?</p>
              <p className="logout-warning">You will need to sign in again to access your medical data.</p>
            </div>
            <div className="logout-modal-footer">
              <button className="logout-cancel-button" onClick={cancelLogout}>
                Cancel
              </button>
              <button className="logout-confirm-button" onClick={handleLogout}>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Profile
