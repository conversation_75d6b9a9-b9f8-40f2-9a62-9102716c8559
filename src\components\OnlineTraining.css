.online-training {
  background: #ffffff;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-top: 1.5rem;
}

.training-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.training-charts {
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
}

.chart-container {
  flex: 1;
  max-width: 190px;
  min-height: 190px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: visible;
  padding: 15px;
}

/* Center labels are now rendered by Highcharts renderer; overlay removed */
.chart-score { display: none; }
.score-value { display: none; }

.score-label {
  display: none;
}

/* Very small mobile screens */
@media (max-width: 360px) {
  .online-training {
    padding: 1rem;
    margin-top: 1rem;
  }

  .training-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .training-charts {
    flex-direction: row;
    gap: 0.5rem;
    justify-content: space-between;
  }

  .chart-container {
    flex: 1;
    max-width: 120px;
    min-height: 120px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .score-value {
    font-size: 1.1rem;
  }

  .chart-score {
    width: 60px;
    top: 52%;
    left: 50%;
  }
}

/* Mobile optimizations */
@media (min-width: 361px) and (max-width: 479px) {
  .online-training {
    padding: 1.25rem;
    margin-top: 1rem;
  }

  .training-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .training-charts {
    flex-direction: row;
    gap: 1rem;
    justify-content: space-between;
  }

  .chart-container {
    flex: 1;
    max-width: 140px;
    min-height: 140px;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .score-value {
    font-size: 1.3rem;
  }

  .chart-score {
    width: 70px;
    top: 52%;
    left: 50%;
  }
}

/* Small tablets and large phones */
@media (min-width: 480px) and (max-width: 767px) {
  .online-training {
    padding: 1.4rem;
  }

  .training-title {
    font-size: 1.2rem;
  }

  .training-charts {
    gap: 1.25rem;
  }

  .chart-container {
    max-width: 170px;
    min-height: 170px;
    padding: 15px;
  }

  .score-value {
    font-size: 1.6rem;
  }
}

/* Tablets */
@media (min-width: 768px) and (max-width: 1023px) {
  .online-training {
    padding: 1.5rem;
  }

  .training-title {
    font-size: 1.3rem;
  }

  .training-charts {
    gap: 1.5rem;
  }

  .chart-container {
    max-width: 180px;
    min-height: 180px;
    padding: 15px;
  }

  .score-value {
    font-size: 1.8rem;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .online-training {
    padding: 2rem;
  }

  .training-title {
    font-size: 1.4rem;
    margin-bottom: 2rem;
  }

  .training-charts {
    gap: 2rem;
  }

  .chart-container {
    max-width: 190px;
    min-height: 190px;
    padding: 15px;
  }

  .score-value {
    font-size: 2rem;
  }
}

/* Chart specific styling */
.highcharts-container {
  width: 100% !important;
  margin: 0 auto;
}

.highcharts-root {
  font-family: inherit !important;
}

.highcharts-title {
  font-family: inherit !important;
}

.highcharts-background {
  fill: transparent !important;
}

/* Ensure proper chart alignment */
.chart-container > div {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Ensure charts are responsive */
@media (max-width: 360px) {
  .chart-container .highcharts-container {
    height: 110px !important;
    width: 110px !important;
  }
}

@media (min-width: 361px) and (max-width: 479px) {
  .chart-container .highcharts-container {
    height: 130px !important;
    width: 130px !important;
  }
}

@media (min-width: 480px) and (max-width: 767px) {
  .chart-container .highcharts-container {
    height: 140px !important;
    width: 140px !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .chart-container .highcharts-container {
    height: 145px !important;
    width: 145px !important;
  }
}

@media (min-width: 1024px) {
  .chart-container .highcharts-container {
    height: 150px !important;
    width: 150px !important;
  }
}
