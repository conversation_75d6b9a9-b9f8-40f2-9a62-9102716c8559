/* Shared page styles */
.page-container {
  padding: 1rem 0;
  animation: fadeIn 0.5s ease-in-out;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.page-subtitle {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
  font-weight: 400;
}

/* Dashboard specific styles */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  padding: 0 0.5rem;
}

.dashboard-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
}

.card-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-bottom: 1rem;
}

.dashboard-icon {
  font-size: 1.5rem;
  color: white;
}

/* Search specific styles */
.search-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 1rem;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: #6c757d;
  font-size: 1.25rem;
}

.search-input {
  flex: 1;
  padding: 1rem 1rem 1rem 3rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  outline: none;
}

.filter-button {
  padding: 1rem;
  border: none;
  background: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 1.25rem;
}

.recent-searches {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  border: 1px solid #f1f3f4;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.section-icon {
  font-size: 1.25rem;
}

.search-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.search-tag {
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
}

.search-tag:hover {
  background: #e9ecef;
  color: #1a252f;
  border-color: #dee2e6;
}

.search-tag:active {
  background: #dee2e6;
  transform: translateY(1px);
}

/* Offline Modules specific styles */
.storage-info {
  margin-bottom: 2rem;
  padding: 0 1rem;
}

.storage-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.storage-icon {
  font-size: 2rem;
  color: #667eea;
}

.storage-details h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.storage-details p {
  margin: 0 0 0.5rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.storage-bar {
  width: 200px;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.storage-progress {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.modules-list {
  padding: 0 1rem;
}

.module-item {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-title {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
}

.module-size {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.downloaded-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #28a745;
  font-size: 0.9rem;
}

.offline-icon {
  font-size: 1.25rem;
}

.download-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.download-button:hover {
  background: #0056b3;
}

/* Profile specific styles */
.profile-info {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 0 1rem 2rem 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  gap: 1rem;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 2.5rem;
  color: white;
}

.profile-name {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.profile-email, .profile-role {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.profile-options {
  padding: 0 1rem;
}

.profile-option {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.profile-option:hover {
  transform: translateY(-1px);
}

.logout-option {
  border: 1px solid #dc3545;
  color: #dc3545;
}

.option-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.logout-option .option-icon {
  background: #ffeaea;
}

.option-content {
  flex: 1;
}

.option-title {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1rem;
}

.logout-option .option-title {
  color: #dc3545;
}

.option-description {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.option-arrow {
  color: #6c757d;
}

/* Responsive design */
@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding: 0;
  }
  
  .page-container {
    padding: 2rem 0;
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 800px;
    margin: 0 auto;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
