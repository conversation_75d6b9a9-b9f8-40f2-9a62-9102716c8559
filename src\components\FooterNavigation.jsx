import { Link } from 'react-router-dom'
import { 
  MdDashboard, 
  MdSearch, 
  MdHome, 
  MdOfflinePin, 
  Md<PERSON>erson 
} from 'react-icons/md'
import './FooterNavigation.css'

const FooterNavigation = ({ currentPath }) => {
  const navItems = [
    {
      path: '/dashboard',
      icon: MdDashboard,
      label: 'Dashboard'
    },
    {
      path: '/search',
      icon: MdSearch,
      label: 'Search'
    },
    {
      path: '/home',
      icon: MdHome,
      label: 'Home'
    },
    {
      path: '/offline-modules',
      icon: MdOfflinePin,
      label: 'Offline'
    },
    {
      path: '/profile',
      icon: Md<PERSON><PERSON>,
      label: 'Profile'
    }
  ]

  return (
    <footer className="footer-navigation">
      <nav className="nav-container">
        {navItems.map((item) => {
          const IconComponent = item.icon
          const isActive = currentPath === item.path
          
          return (
            <Link
              key={item.path}
              to={item.path}
              className={`nav-item ${isActive ? 'active' : ''}`}
            >
              <IconComponent className="nav-icon" />
              <span className="nav-label">{item.label}</span>
            </Link>
          )
        })}
      </nav>
    </footer>
  )
}

export default FooterNavigation
